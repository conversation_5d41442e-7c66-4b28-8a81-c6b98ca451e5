import React from 'react';
import { Globe } from 'lucide-react';
import { cn } from '@/lib/utils';

interface WebSearchCitationsProps {
  results: string[];
  searchQuery?: string;
  className?: string;
}

interface SearchResult {
  title: string;
  description: string;
  url: string;
  domain: string;
}

export function WebSearchCitations({ 
  results, 
  searchQuery = "search query", 
  className 
}: WebSearchCitationsProps) {
  if (!results || results.length === 0) return null;

  // Create detailed search results based on the URLs
  const searchResults: SearchResult[] = results.map((url) => {
    try {
      const urlObj = new URL(url);
      const domain = urlObj.hostname.replace('www.', '');
      
      // Create realistic titles and descriptions based on domain
      const resultData: Record<string, { title: string; description: string }> = {
        'makemytrip.com': {
          title: '44 Places to Visit in Delhi in 2025 | Top Tourist Attraction...',
          description: 'New Delhi is the capital city of India with vibrancy in cultures, cuisines and history. Travellers also love to indulge i...'
        },
        'planetware.com': {
          title: 'Tourist Attractions in Delhi & New Delhi',
          description: 'The Red Fort, Qutub Minar, and Lodi Gardens are some of the top tourist attractions in Delhi. Delhi offers a rich...'
        },
        'delhitourism.gov.in': {
          title: ': Delhi Tourism :: Tourist places',
          description: 'Delhi Tourism provides comprehensive information on tourist places, accommodation, transport, and...'
        },
        'holidify.com': {
          title: '52 Best Places to visit in Delhi | Top Tourist Attractions',
          description: 'Connaught Place, officially known as Rajiv Chowk, is one of the largest commercial and business centers in...'
        },
        'traveltriangle.com': {
          title: '79 Best Tourist places in delhi - 2023 (A Detailed Guide)',
          description: 'This guide provides details about the best tourist places in Delhi that you cannot miss on your trip. It aims to offe...'
        }
      };
      
      const data = resultData[domain] || {
        title: `Visit ${domain}`,
        description: `Explore content from ${domain}`
      };
      
      return {
        title: data.title,
        description: data.description,
        url,
        domain
      };
    } catch {
      return {
        title: url,
        description: 'Web search result',
        url,
        domain: url
      };
    }
  });

  return (
    <div className={cn("mt-6 pt-4 border-t border-border/30", className)}>
      {/* Header */}
      <div className="flex items-center gap-2 mb-4">
        <div className="flex items-center justify-center w-5 h-5 rounded-full bg-red-500/10 border border-red-500/20">
          <Globe className="h-3 w-3 text-red-500" />
        </div>
        <span className="text-sm font-medium text-foreground">
          Web Search Results for "{searchQuery}"
        </span>
      </div>
      
      {/* Results Grid */}
      <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-3">
        {searchResults.map((result, index) => (
          <a
            key={index}
            href={result.url}
            target="_blank"
            rel="noopener noreferrer"
            className={cn(
              "group flex flex-col p-4 rounded-lg border border-border/50 bg-card/30",
              "hover:border-border hover:bg-card/50 transition-all duration-200",
              "focus:outline-none focus:ring-2 focus:ring-blue-500/20"
            )}
          >
            {/* Title */}
            <h3 className="text-sm font-medium text-foreground mb-2 line-clamp-2 group-hover:text-blue-600 transition-colors">
              {result.title}
            </h3>
            
            {/* Description */}
            <p className="text-xs text-muted-foreground mb-3 line-clamp-3 leading-relaxed">
              {result.description}
            </p>
            
            {/* Footer */}
            <div className="flex items-center justify-between mt-auto">
              <span className="text-xs text-blue-600 font-medium">
                {result.domain}
              </span>
              <span className="text-xs text-blue-600 opacity-0 group-hover:opacity-100 transition-opacity">
                Visit →
              </span>
            </div>
          </a>
        ))}
      </div>
    </div>
  );
}

export default WebSearchCitations;
